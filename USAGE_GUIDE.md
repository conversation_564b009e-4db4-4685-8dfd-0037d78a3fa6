# Garden Planner Web Application - Complete Usage Guide

## Overview

The Garden Planner is a comprehensive web application designed to help you manage your garden properties, plants, seasonal planning, and collaborative household management. This guide will walk you through all the features and how to use them effectively.

## Getting Started

### 1. Registration and First Login

1. Navigate to the application URL (typically http://127.0.0.1:8080)
2. Click "Register" to create a new account
3. Enter your username and password
4. The first user automatically becomes a superadmin with full access
5. Login with your credentials to access the dashboard

### 2. Initial Setup

After logging in for the first time:
- You'll be taken to the main dashboard
- The system automatically creates a default household for you
- You can start by adding your first property using the Property Wizard

## Core Features

### Property Management

#### Creating Properties with the Property Wizard

1. Navigate to **Properties** → **Property Wizard**
2. **Step 1: Basic Information**
   - Enter property name
   - Specify number of floors
   - Set inside and outside area measurements
3. **Step 2: Property Boundaries**
   - Use the drawing tools to outline your property
   - Choose from rectangle, circle, or freehand drawing
   - Toggle grid for precise placement
4. **Step 3: Growing Areas**
   - Define specific areas where you'll grow plants
   - Add multiple growing areas per floor
   - Each area can have custom shapes and sizes

#### Viewing and Managing Properties

- **Property List**: View all your properties with quick stats
- **Property Details**: Click on any property to see detailed information
- **Property Visualization**: Interactive 2D canvas showing property layout
- **Edit Properties**: Modify property details and layouts anytime

### Plant and Seed Management

#### Adding Plants

1. Go to **Plants** → **Add New Plant**
2. Enter plant details:
   - Name and description
   - Growing requirements
   - Care instructions
3. Link to HerbaDB for automatic botanical information

#### HerbaDB Integration

- **Automatic Plant Data**: The system can automatically gather plant information
- **Scientific Information**: Access to botanical data, care requirements, and growing tips
- **Admin Access**: Admins can manage the global plant database

#### Seed Inventory

1. Navigate to **Seeds** → **Add New Seed**
2. Track seed information:
   - Acquisition year and expiration
   - Origin and notes
   - Link to parent plant in HerbaDB

### Season Planning

#### Creating Season Plans

1. Go to **Season Plans** → **New Plan**
2. Select:
   - Season and date range
   - Target property and growing area
   - Description and goals
3. Add plants to your plan with specific quantities and positions

#### Automatic Season Planning

- **AI-Powered Recommendations**: Get optimal plant placement suggestions
- **Companion Planting**: System suggests beneficial plant combinations
- **Yield Optimization**: Maximize your harvest with intelligent planning
- **Nutrient Rotation**: Maintain soil health with crop rotation suggestions

#### Season Plan Visualization

- **3D Property View**: See your planned garden in 3D
- **2D Layout**: Traditional top-down view with plant markers
- **Interactive Elements**: Click on plants for detailed information

### Household Management

#### Creating and Managing Households

1. Click on your username → **Manage Households**
2. **Create New Household**: Set up additional garden spaces
3. **Invite Members**: Share your garden with family or friends
4. **Role Management**: Assign different permission levels:
   - **Superadmin**: Full system access
   - **Admin**: Household management
   - **Moderator**: Content management
   - **Commenter**: Can add comments
   - **Viewer**: Read-only access

#### Household Sharing

- **Edit Access**: Full modification rights
- **Temporary Care**: Limited access for plant sitting
- **View Access**: Read-only for sharing progress
- **Comment Access**: Can add notes and suggestions

### Wishlist System

#### Plant Wishlist

1. Navigate to **Wishlist** → **Plants**
2. Browse available plants
3. Click **Add to Wishlist** for plants you want to grow
4. Organize by priority and add notes

#### Seed Wishlist

1. Go to **Wishlist** → **Seeds**
2. Add seeds you want to acquire
3. Track acquisition goals and sources

### Notification System

#### Automatic Notifications

The system automatically creates notifications for:
- **Watering Reminders**: Based on plant-specific needs
- **Fertilizing Schedule**: Customized for each plant type
- **Harvest Alerts**: When crops are ready
- **Season Planning**: Optimal planting times
- **General Care**: Pruning, pest checks, etc.

#### Managing Notifications

1. View notifications in the top navigation
2. Mark tasks as complete to update schedules
3. Customize notification frequency in settings

## Advanced Features

### Admin Dashboard (Admin/Superadmin Only)

#### User Management
- Create and manage user accounts
- Assign roles and permissions
- Monitor user activity

#### Database Management
- Backup and restore data
- Optimize database performance
- Export data for analysis

#### HerbaDB Administration
- Manage global plant database
- Scrape botanical information from public sources
- Maintain data quality and accuracy

### Property Visualization

#### 3D Visualization
- Interactive 3D models of your properties
- Rotate and zoom for detailed views
- Real-time updates as you modify layouts

#### 2D Canvas Tools
- Precise drawing tools for property layouts
- Grid system for accurate measurements
- Layer management for complex designs

## Tips and Best Practices

### Getting the Most from Your Garden Planner

1. **Start Small**: Begin with one property and expand gradually
2. **Use the Wizard**: The Property Wizard ensures you capture all necessary details
3. **Link to HerbaDB**: Always connect plants to botanical data for better care recommendations
4. **Plan Seasonally**: Use the automatic season planner for optimal results
5. **Share Wisely**: Invite household members with appropriate permission levels
6. **Stay Organized**: Use the wishlist to plan future garden expansions

### Troubleshooting Common Issues

#### Property Visualization Not Loading
- Ensure your browser supports modern JavaScript
- Try refreshing the page
- Check that property shapes are properly defined

#### Notifications Not Appearing
- Verify your notification settings
- Check that plants have proper care schedules
- Ensure the notification service is running

#### Permission Issues
- Contact your household admin for role adjustments
- Verify you're logged into the correct account
- Check household membership status

## Mobile Usage

The Garden Planner is fully responsive and works on mobile devices:
- **Touch-Friendly Interface**: All tools work with touch input
- **Mobile Navigation**: Collapsible menu for small screens
- **Offline Capability**: Basic functionality available without internet

## Data Privacy and Security

- **Local Data**: All your garden data stays on your server
- **Secure Authentication**: Password-based login with session management
- **Role-Based Access**: Granular permissions protect your information
- **Data Export**: You can export your data anytime

## Support and Community

### Getting Help
- Check this guide for common questions
- Contact your system administrator for technical issues
- Join the community forums for tips and advice

### Contributing
- Report bugs through the admin interface
- Suggest features via the feedback system
- Share your garden success stories

## Conclusion

The Garden Planner is designed to grow with your gardening journey. Whether you're a beginner with a small herb garden or an experienced gardener managing multiple properties, the system adapts to your needs. Take advantage of the automation features to reduce manual work, and use the collaborative tools to share your passion for gardening with others.

Happy gardening! 🌱
