{% extends "base.html" %}

{% block title %}Season Plans{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto">
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-sage-900 dark:text-sage-100">Season Plans</h1>
            <p class="text-sage-600 dark:text-sage-400 mt-2">Manage your seasonal growing plans and schedules</p>
        </div>
        <div class="flex space-x-3">
            <a href="/season_plans/auto_plan" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors shadow-md">
                🤖 Auto Plan
            </a>
            <a href="/season_plans/new" class="bg-sage-600 hover:bg-sage-700 text-white px-4 py-2 rounded-md transition-colors shadow-md">
                Create New Plan
            </a>
        </div>
    </div>

    <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md overflow-hidden">
        {% if plans %}
        <div class="px-6 py-4 border-b border-sage-200 dark:border-sage-700">
            <h2 class="text-xl font-semibold text-sage-900 dark:text-sage-100">Your Season Plans</h2>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-sage-200 dark:divide-sage-700">
                <thead class="bg-sage-50 dark:bg-sage-700">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sage-500 dark:text-sage-300 uppercase tracking-wider">
                            Plan Name
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sage-500 dark:text-sage-300 uppercase tracking-wider">
                            Season
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sage-500 dark:text-sage-300 uppercase tracking-wider">
                            Property
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sage-500 dark:text-sage-300 uppercase tracking-wider">
                            Date Range
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sage-500 dark:text-sage-300 uppercase tracking-wider">
                            Plants
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-sage-500 dark:text-sage-300 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-sage-800 divide-y divide-sage-200 dark:divide-sage-700">
                    {% for plan in plans %}
                    <tr class="hover:bg-sage-50 dark:hover:bg-sage-700 transition-colors">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-sage-900 dark:text-sage-100">{{ plan.name }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-sage-900 dark:text-sage-100">{{ plan.season_name }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-sage-900 dark:text-sage-100">{{ plan.property_name }}</div>
                            {% if plan.growing_area %}
                            <div class="text-xs text-sage-500 dark:text-sage-400">
                                {% if plan.growing_area.name %}
                                    {{ plan.growing_area.name }}
                                {% else %}
                                    Area {{ plan.growing_area.id }}
                                {% endif %}
                            </div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-sage-900 dark:text-sage-100">{{ plan.start_date }} to {{ plan.end_date }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-sage-900 dark:text-sage-100">{% if plan.plant_count %}{{ plan.plant_count }}{% else %}0{% endif %}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div class="flex justify-end space-x-2">
                                <a href="/season_plans/{{ plan.id }}/view" class="text-sage-600 hover:text-sage-900 dark:text-sage-400 dark:hover:text-sage-300">View</a>
                                <a href="/season_plans/{{ plan.id }}/edit" class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">Edit</a>
                                <form method="post" action="/season_plans/{{ plan.id }}/delete" class="inline">
                                    <button type="submit" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                                        onclick="return confirm('Are you sure you want to delete this plan?')">
                                        Delete
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-12">
            <div class="text-sage-400 dark:text-sage-500 mb-4">
                <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
            </div>
            <p class="text-sage-600 dark:text-sage-400 mb-4 text-lg">No season plans found</p>
            <p class="text-sage-500 dark:text-sage-500 mb-6">Start planning your growing seasons to optimize your garden's productivity</p>
            <div class="flex justify-center space-x-3">
                <a href="/season_plans/new" class="bg-sage-600 hover:bg-sage-700 text-white px-4 py-2 rounded-md transition-colors font-medium">
                    Create your first season plan
                </a>
                <a href="/season_plans/auto_plan" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors font-medium">
                    🤖 Try Auto Planning
                </a>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
