{% extends "base.html" %}

{% block title %}{{ plan.name }} - Season Plan{% endblock %}

{% block head %}
<!-- Three.js for 3D visualization -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>
<script src="/static/js/property-visualizer.js"></script>
{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-sage-900 dark:text-sage-100">{{ plan.name }}</h1>
            <p class="text-sage-600 dark:text-sage-400 mt-1">
                {{ plan.start_date }} to {{ plan.end_date }}
                {% if plan.property %} • {{ plan.property.name }}{% endif %}
            </p>
        </div>
        <div class="space-x-2">
            <a href="/season_plans/{{ plan.id }}/edit" class="bg-sage-600 hover:bg-sage-700 text-white font-medium py-2 px-4 rounded transition-colors">
                Edit Plan
            </a>
            <form method="post" action="/season_plans/{{ plan.id }}/delete" class="inline">
                <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded transition-colors"
                    onclick="return confirm('Are you sure you want to delete this plan?')">
                    Delete Plan
                </button>
            </form>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Plan Details -->
        <div class="lg:col-span-1">
            <div class="bg-white dark:bg-sage-800 shadow-md rounded-lg p-6 mb-6">
                <h2 class="text-xl font-semibold text-sage-900 dark:text-sage-100 mb-4">Plan Details</h2>

                <div class="mb-4">
                    <p class="text-sm text-sage-600 dark:text-sage-400">Season</p>
                    <p class="text-md font-medium text-sage-900 dark:text-sage-100">{{ plan.season_name }}</p>
                </div>

                <div class="mb-4">
                    <p class="text-sm text-sage-600 dark:text-sage-400">Property</p>
                    <p class="text-md font-medium text-sage-900 dark:text-sage-100">{{ plan.property_name }}</p>
                </div>

                {% if plan.growing_area %}
                <div class="mb-4">
                    <p class="text-sm text-sage-600 dark:text-sage-400">Growing Area</p>
                    <p class="text-md font-medium text-sage-900 dark:text-sage-100">
                        {% if plan.growing_area.name %}
                            {{ plan.growing_area.name }}
                        {% else %}
                            Area {{ plan.growing_area.id }}
                        {% endif %}
                    </p>
                </div>
                {% endif %}

                <div class="mb-4">
                    <p class="text-sm text-sage-600 dark:text-sage-400">Date Range</p>
                    <p class="text-md font-medium text-sage-900 dark:text-sage-100">{{ plan.start_date }} to {{ plan.end_date }}</p>
                </div>

                {% if plan.description %}
                <div class="mb-4">
                    <p class="text-sm text-sage-600 dark:text-sage-400">Description</p>
                    <p class="text-md text-sage-900 dark:text-sage-100">{{ plan.description }}</p>
                </div>
                {% endif %}
            </div>

            <!-- Add Plant Form -->
            <div class="bg-white dark:bg-sage-800 shadow-md rounded-lg p-6">
                <h2 class="text-xl font-semibold text-sage-900 dark:text-sage-100 mb-4">Add Plant to Plan</h2>

                <form method="post" action="/season_plans/{{ plan.id }}/add_plant">
                    <div class="mb-4">
                        <label for="plant_id" class="block text-sm font-medium text-sage-700 dark:text-sage-300 mb-1">Plant</label>
                        <select id="plant_id" name="plant_id" required
                            class="w-full px-3 py-2 border border-sage-300 dark:border-sage-600 rounded-md shadow-sm focus:outline-none focus:ring-sage-500 focus:border-sage-500 bg-white dark:bg-sage-700 text-sage-900 dark:text-sage-100">
                            <option value="">Select a plant</option>
                            {% for plant in plan.available_plants %}
                            <option value="{{ plant.id }}">{{ plant.name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="mb-4">
                        <label for="quantity" class="block text-sm font-medium text-sage-700 dark:text-sage-300 mb-1">Quantity</label>
                        <input type="number" id="quantity" name="quantity" min="1" value="1" required
                            class="w-full px-3 py-2 border border-sage-300 dark:border-sage-600 rounded-md shadow-sm focus:outline-none focus:ring-sage-500 focus:border-sage-500 bg-white dark:bg-sage-700 text-sage-900 dark:text-sage-100">
                    </div>

                    {% if plan.growing_area %}
                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div>
                            <label for="position_x" class="block text-sm font-medium text-sage-700 dark:text-sage-300 mb-1">Position X</label>
                            <input type="number" id="position_x" name="position_x"
                                class="w-full px-3 py-2 border border-sage-300 dark:border-sage-600 rounded-md shadow-sm focus:outline-none focus:ring-sage-500 focus:border-sage-500 bg-white dark:bg-sage-700 text-sage-900 dark:text-sage-100">
                        </div>
                        <div>
                            <label for="position_y" class="block text-sm font-medium text-sage-700 dark:text-sage-300 mb-1">Position Y</label>
                            <input type="number" id="position_y" name="position_y"
                                class="w-full px-3 py-2 border border-sage-300 dark:border-sage-600 rounded-md shadow-sm focus:outline-none focus:ring-sage-500 focus:border-sage-500 bg-white dark:bg-sage-700 text-sage-900 dark:text-sage-100">
                        </div>
                    </div>
                    {% endif %}

                    <div class="mb-4">
                        <label for="notes" class="block text-sm font-medium text-sage-700 dark:text-sage-300 mb-1">Notes (Optional)</label>
                        <textarea id="notes" name="notes" rows="2"
                            class="w-full px-3 py-2 border border-sage-300 dark:border-sage-600 rounded-md shadow-sm focus:outline-none focus:ring-sage-500 focus:border-sage-500 bg-white dark:bg-sage-700 text-sage-900 dark:text-sage-100"></textarea>
                    </div>

                    <button type="submit" class="w-full bg-sage-600 hover:bg-sage-700 text-white font-bold py-2 px-4 rounded transition-colors">
                        Add Plant
                    </button>
                </form>
            </div>
        </div>

        <!-- Plants List -->
        <div class="lg:col-span-2">
            <div class="bg-white dark:bg-sage-800 shadow-md rounded-lg p-6">
                <h2 class="text-xl font-semibold text-sage-900 dark:text-sage-100 mb-4">Plants in this Plan</h2>

                {% if plan.plants %}
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-sage-200 dark:divide-sage-600">
                        <thead class="bg-sage-50 dark:bg-sage-700">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sage-600 dark:text-sage-300 uppercase tracking-wider">Plant</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sage-600 dark:text-sage-300 uppercase tracking-wider">Quantity</th>
                                {% if plan.growing_area %}
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sage-600 dark:text-sage-300 uppercase tracking-wider">Position</th>
                                {% endif %}
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sage-600 dark:text-sage-300 uppercase tracking-wider">Notes</th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-sage-600 dark:text-sage-300 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-sage-800 divide-y divide-sage-200 dark:divide-sage-600">
                            {% for plant in plan.plants %}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-sage-900 dark:text-sage-100">{{ plant.name }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-sage-900 dark:text-sage-100">{{ plant.quantity }}</div>
                                </td>
                                {% if plan.growing_area %}
                                <td class="px-6 py-4 whitespace-nowrap">
                                    {% if plant.position_x is not none and plant.position_y is not none %}
                                    <div class="text-sm text-sage-900 dark:text-sage-100">({{ plant.position_x }}, {{ plant.position_y }})</div>
                                    {% else %}
                                    <div class="text-sm text-sage-500 dark:text-sage-400">Not specified</div>
                                    {% endif %}
                                </td>
                                {% endif %}
                                <td class="px-6 py-4">
                                    <div class="text-sm text-sage-900 dark:text-sage-100">{% if plant.notes %}{{ plant.notes }}{% endif %}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <a href="/season_plans/{{ plan.id }}/edit_plant/{{ plant.id }}" class="text-sage-600 hover:text-sage-800 dark:text-sage-400 dark:hover:text-sage-200 mr-3">Edit</a>
                                    <form method="post" action="/season_plans/{{ plan.id }}/remove_plant/{{ plant.id }}" class="inline">
                                        <button type="submit" class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-200"
                                            onclick="return confirm('Are you sure you want to remove this plant?')">
                                            Remove
                                        </button>
                                    </form>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <p class="text-sage-500 dark:text-sage-400">No plants have been added to this plan yet.</p>
                </div>
                {% endif %}
            </div>

            {% if plan.growing_area %}
    <!-- Property Visualization with Plants -->
    <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md p-6 mb-8">
        <h2 class="text-xl font-semibold text-sage-900 dark:text-sage-100 mb-4">Property Layout with Plants</h2>

        <!-- View Toggle -->
        <div class="flex space-x-2 mb-4">
            <button class="view-tab px-4 py-2 rounded bg-sage-600 text-white" data-tab="3d">3D View</button>
            <button class="view-tab px-4 py-2 rounded bg-sage-200 text-sage-700" data-tab="2d">2D View</button>
        </div>

        <!-- 3D Visualization Container -->
        <div id="visualization-container" class="w-full h-96 bg-sage-100 dark:bg-sage-900 rounded-lg mb-4"></div>

        <!-- 2D Canvas Container -->
        <div id="canvas-container" class="w-full h-96 bg-sage-100 dark:bg-sage-900 rounded-lg mb-4 hidden">
            <canvas id="viewCanvas" class="w-full h-full"></canvas>
        </div>

        <!-- Plant Markers for 2D View -->
        <div id="plant-markers" class="hidden">
            {% for plant in plan.plants %}
                {% if plant.position_x is not none and plant.position_y is not none %}
                <div class="plant-marker"
                     data-plant-id="{{ plant.id }}"
                     data-plant-name="{{ plant.name }}"
                     data-position-x="{{ plant.position_x }}"
                     data-position-y="{{ plant.position_y }}"
                     data-quantity="{{ plant.quantity }}"
                     style="position: absolute; left: {{ plant.position_x }}%; top: {{ plant.position_y }}%;">
                    <div class="w-8 h-8 bg-sage-500 rounded-full flex items-center justify-center text-white font-bold cursor-pointer"
                         title="{{ plant.name }} ({{ plant.quantity }})">
                        {{ plant.name | truncate(length=1) }}
                    </div>
                </div>
                {% endif %}
            {% endfor %}
        </div>

        <!-- Controls -->
        <div class="flex space-x-2">
            <button id="reset-camera-btn" class="bg-sage-500 hover:bg-sage-600 text-white px-3 py-1 rounded text-sm">Reset View</button>
            <button id="toggle-stats-btn" class="bg-sage-500 hover:bg-sage-600 text-white px-3 py-1 rounded text-sm">Toggle Stats</button>
        </div>
    </div>

                <div class="mt-2 text-sm text-gray-500">
                    <p>Click on a plant marker to see details.</p>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tab switching
    const tabs = document.querySelectorAll('.view-tab');
    const viewContainers = {
        '3d': document.getElementById('visualization-container'),
        '2d': document.getElementById('canvas-container')
    };

    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const targetTab = this.dataset.tab;

            // Update tab styles
            tabs.forEach(t => {
                t.classList.remove('bg-sage-600', 'text-white');
                t.classList.add('bg-sage-200', 'text-sage-700');
            });
            this.classList.remove('bg-sage-200', 'text-sage-700');
            this.classList.add('bg-sage-600', 'text-white');

            // Show/hide containers
            Object.keys(viewContainers).forEach(key => {
                if (key === targetTab) {
                    viewContainers[key].classList.remove('hidden');
                } else {
                    viewContainers[key].classList.add('hidden');
                }
            });

            if (targetTab === '2d') {
                setTimeout(() => {
                    resizeCanvas();
                    drawLayout();
                }, 100);
            }
        });
    });

    // 2D Canvas rendering
    const canvas = document.getElementById('viewCanvas');
    const ctx = canvas.getContext('2d');

    function resizeCanvas() {
        const container = canvas.parentElement;
        canvas.width = container.clientWidth;
        canvas.height = container.clientHeight;
        drawLayout();
    }

    window.addEventListener('resize', resizeCanvas);

    function drawLayout() {
        // Clear canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // Draw property boundaries if available
        {% if property_shapes %}
        const propertyShapes = {{ property_shapes|safe }};
        propertyShapes.forEach(shape => {
            drawShape(shape, 'rgba(128, 128, 128, 0.2)');
        });
        {% endif %}

        // Draw growing areas if available
        {% if growing_areas %}
        const growingAreas = {{ growing_areas|safe }};
        growingAreas.forEach(shape => {
            drawShape(shape, 'rgba(76, 175, 80, 0.3)');
        });
        {% endif %}

        // Draw plant markers
        const plantMarkers = document.querySelectorAll('.plant-marker');
        plantMarkers.forEach(marker => {
            const x = parseFloat(marker.dataset.positionX) / 100 * canvas.width;
            const y = parseFloat(marker.dataset.positionY) / 100 * canvas.height;
            const name = marker.dataset.plantName;
            const quantity = marker.dataset.quantity;

            // Draw plant circle
            ctx.beginPath();
            ctx.arc(x, y, 15, 0, 2 * Math.PI);
            ctx.fillStyle = 'rgba(76, 175, 80, 0.8)';
            ctx.fill();
            ctx.strokeStyle = 'rgba(76, 175, 80, 1)';
            ctx.lineWidth = 2;
            ctx.stroke();

            // Draw plant label
            ctx.fillStyle = 'white';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(name.charAt(0), x, y + 4);
        });
    }

    function drawShape(shape, color) {
        try {
            const shapeData = typeof shape.shape_data === 'string'
                ? JSON.parse(shape.shape_data)
                : shape.shape_data;

            const points = shapeData.points || [];
            if (!points.length) return;

            ctx.beginPath();
            ctx.fillStyle = color;
            ctx.strokeStyle = 'rgba(0, 0, 0, 0.5)';
            ctx.lineWidth = 2;

            ctx.moveTo(points[0].x, points[0].y);
            for (let i = 1; i < points.length; i++) {
                ctx.lineTo(points[i].x, points[i].y);
            }
            ctx.closePath();
            ctx.fill();
            ctx.stroke();
        } catch (e) {
            console.error('Error drawing shape:', e);
        }
    }

    // Initialize 3D visualization
    try {
        const visualizer = new PropertyVisualizer('visualization-container');

        // Convert property data for visualizer
        const propertyData = {
            id: {{ plan.property.id | default(value=0) }},
            name: "{{ plan.property.name | default(value='Unknown Property') }}",
            floors: {{ plan.property.floors | default(value=1) }},
            propertyShapes: {% if property_shapes %}{{ property_shapes|safe }}{% else %}[]{% endif %},
            growingAreas: {% if growing_areas %}{{ growing_areas|safe }}{% else %}[]{% endif %}
        };

        visualizer.loadPropertyData(propertyData);

        // Control buttons
        document.getElementById('reset-camera-btn').addEventListener('click', function() {
            visualizer.resetCamera();
        });

        document.getElementById('toggle-stats-btn').addEventListener('click', function() {
            visualizer.options.showStats = !visualizer.options.showStats;
            visualizer.updateStatsDisplay();
        });

    } catch (e) {
        console.error('Error initializing 3D visualizer:', e);
        // Fall back to 2D view if 3D fails
        document.querySelector('[data-tab="2d"]').click();
    }

    // Plant marker click handler
    const plantMarkers = document.querySelectorAll('.plant-marker');
    plantMarkers.forEach(marker => {
        marker.addEventListener('click', function() {
            const plantId = this.dataset.plantId;
            const plantName = this.dataset.plantName;
            const quantity = this.dataset.quantity;

            // Create a better modal instead of alert
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white dark:bg-sage-800 rounded-lg p-6 max-w-md mx-4">
                    <h3 class="text-lg font-semibold text-sage-900 dark:text-sage-100 mb-4">${plantName}</h3>
                    <p class="text-sage-700 dark:text-sage-300 mb-4">Quantity: ${quantity}</p>
                    <button onclick="this.closest('.fixed').remove()" class="bg-sage-600 hover:bg-sage-700 text-white px-4 py-2 rounded">Close</button>
                </div>
            `;
            document.body.appendChild(modal);
        });
    });
});
</script>
{% endblock %}
