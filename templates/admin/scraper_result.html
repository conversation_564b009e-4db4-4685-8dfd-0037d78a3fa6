{% extends "base.html" %}
{% block title %}Plant Scraper Results - Admin{% endblock %}
{% block content %}
<div class="max-w-4xl mx-auto">
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-sage-900 dark:text-sage-100">Plant Information Scraped</h1>
            <p class="text-sage-600 dark:text-sage-400 mt-2">Results for: {{ plant_name }}</p>
        </div>
        <div class="flex space-x-3">
            <a href="/admin/herba-db" class="bg-sage-500 hover:bg-sage-600 text-white px-4 py-2 rounded transition-colors">
                Back to HerbaDB
            </a>
        </div>
    </div>

    <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md p-6 mb-6">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Basic Information -->
            <div class="space-y-4">
                <h2 class="text-xl font-semibold text-sage-900 dark:text-sage-100 mb-4">Basic Information</h2>
                
                <div>
                    <label class="block text-sm font-medium text-sage-700 dark:text-sage-300 mb-1">Plant Name</label>
                    <p class="text-sage-900 dark:text-sage-100 bg-sage-50 dark:bg-sage-700 p-2 rounded">{{ plant_info.name }}</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-sage-700 dark:text-sage-300 mb-1">Scientific Name</label>
                    <p class="text-sage-900 dark:text-sage-100 bg-sage-50 dark:bg-sage-700 p-2 rounded">{{ plant_info.scientific_name | default(value="Not specified") }}</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-sage-700 dark:text-sage-300 mb-1">Common Names</label>
                    <p class="text-sage-900 dark:text-sage-100 bg-sage-50 dark:bg-sage-700 p-2 rounded">{{ plant_info.common_names | join(sep=", ") | default(value="None specified") }}</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-sage-700 dark:text-sage-300 mb-1">Plant Type</label>
                    <p class="text-sage-900 dark:text-sage-100 bg-sage-50 dark:bg-sage-700 p-2 rounded">{{ plant_info.plant_type | default(value="Not specified") }}</p>
                </div>
            </div>

            <!-- Growing Information -->
            <div class="space-y-4">
                <h2 class="text-xl font-semibold text-sage-900 dark:text-sage-100 mb-4">Growing Information</h2>
                
                <div>
                    <label class="block text-sm font-medium text-sage-700 dark:text-sage-300 mb-1">Watering Needs</label>
                    <p class="text-sage-900 dark:text-sage-100 bg-sage-50 dark:bg-sage-700 p-2 rounded">{{ plant_info.watering_needs | default(value="Not specified") }}</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-sage-700 dark:text-sage-300 mb-1">Light Requirements</label>
                    <p class="text-sage-900 dark:text-sage-100 bg-sage-50 dark:bg-sage-700 p-2 rounded">{{ plant_info.light_requirements | default(value="Not specified") }}</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-sage-700 dark:text-sage-300 mb-1">Soil Type</label>
                    <p class="text-sage-900 dark:text-sage-100 bg-sage-50 dark:bg-sage-700 p-2 rounded">{{ plant_info.soil_type | default(value="Not specified") }}</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-sage-700 dark:text-sage-300 mb-1">Hardiness Zone</label>
                    <p class="text-sage-900 dark:text-sage-100 bg-sage-50 dark:bg-sage-700 p-2 rounded">{{ plant_info.hardiness_zone | default(value="Not specified") }}</p>
                </div>
            </div>
        </div>

        <!-- Description -->
        {% if plant_info.description %}
        <div class="mt-6">
            <h3 class="text-lg font-semibold text-sage-900 dark:text-sage-100 mb-2">Description</h3>
            <p class="text-sage-700 dark:text-sage-300 bg-sage-50 dark:bg-sage-700 p-4 rounded">{{ plant_info.description }}</p>
        </div>
        {% endif %}

        <!-- Special Features -->
        {% if plant_info.special_features and plant_info.special_features|length > 0 %}
        <div class="mt-6">
            <h3 class="text-lg font-semibold text-sage-900 dark:text-sage-100 mb-2">Special Features</h3>
            <div class="flex flex-wrap gap-2">
                {% for feature in plant_info.special_features %}
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-sage-100 dark:bg-sage-700 text-sage-800 dark:text-sage-200">
                    {{ feature }}
                </span>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- Care Instructions -->
        {% if plant_info.care_instructions %}
        <div class="mt-6">
            <h3 class="text-lg font-semibold text-sage-900 dark:text-sage-100 mb-2">Care Instructions</h3>
            <p class="text-sage-700 dark:text-sage-300 bg-sage-50 dark:bg-sage-700 p-4 rounded">{{ plant_info.care_instructions }}</p>
        </div>
        {% endif %}

        <!-- Status Message -->
        <div id="status-message" class="mt-6"></div>

        <!-- Action Buttons -->
        <div class="mt-8 flex justify-between items-center">
            <div class="flex space-x-3">
                <button id="create-herba-btn" onclick="createHerbaEntry()" class="bg-sage-600 hover:bg-sage-700 disabled:bg-sage-400 disabled:cursor-not-allowed text-white px-4 py-2 rounded transition-colors">
                    Create HerbaDB Entry
                </button>
                <button onclick="rescrape()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded transition-colors">
                    Re-scrape Information
                </button>
            </div>
            <a href="/admin/herba-db" class="bg-sage-300 hover:bg-sage-400 text-sage-700 px-4 py-2 rounded transition-colors">
                Back to HerbaDB
            </a>
        </div>
    </div>
</div>

<script>
function createHerbaEntry() {
    const button = document.getElementById('create-herba-btn');
    const statusDiv = document.getElementById('status-message');

    // Show loading state
    button.disabled = true;
    button.innerHTML = '<svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Creating...';

    fetch('/admin/herba-db/create-from-scrape', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            plant_info: {{ plant_info_json | safe }},
            plant_name: '{{ plant_name }}'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            statusDiv.innerHTML = `
                <div class="bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded-md p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-green-800 dark:text-green-200">Success!</h3>
                            <div class="mt-2 text-sm text-green-700 dark:text-green-300">
                                <p>HerbaDB entry created successfully. <a href="/admin/herba-db" class="font-medium underline hover:text-green-600">View HerbaDB</a></p>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            button.style.display = 'none';
        } else {
            statusDiv.innerHTML = `
                <div class="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800 dark:text-red-200">Error</h3>
                            <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                                <p>${data.error || 'Unknown error occurred'}</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            button.disabled = false;
            button.innerHTML = 'Create HerbaDB Entry';
        }
    })
    .catch(error => {
        statusDiv.innerHTML = `
            <div class="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800 dark:text-red-200">Network Error</h3>
                        <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                            <p>Failed to connect to server. Please try again.</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
        button.disabled = false;
        button.innerHTML = 'Create HerbaDB Entry';
    });
}

function rescrape() {
    if (confirm('Re-scrape information for {{ plant_name }}?')) {
        window.location.href = '/admin/herba-db/scrape?plant_name={{ plant_name | urlencode }}';
    }
}
</script>
{% endblock %}
