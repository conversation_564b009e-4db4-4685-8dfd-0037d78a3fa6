{% extends "base.html" %}
{% block title %}Notification Management - Admin{% endblock %}
{% block content %}
<div class="max-w-6xl mx-auto">
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-sage-900 dark:text-sage-100">Notification Management</h1>
            <p class="text-sage-600 dark:text-sage-400 mt-2">Monitor and manage system notifications</p>
        </div>
        <a href="/admin/dashboard" class="bg-sage-600 hover:bg-sage-700 text-white px-4 py-2 rounded-md transition-colors shadow-md">
            Back to Dashboard
        </a>
    </div>

    <!-- Notification Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold text-sage-900 dark:text-sage-100 mb-2">Total Notifications</h3>
            <p class="text-3xl font-bold text-sage-600 dark:text-sage-400">1,247</p>
        </div>
        <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold text-sage-900 dark:text-sage-100 mb-2">Pending</h3>
            <p class="text-3xl font-bold text-orange-600 dark:text-orange-400">23</p>
        </div>
        <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold text-sage-900 dark:text-sage-100 mb-2">Sent Today</h3>
            <p class="text-3xl font-bold text-green-600 dark:text-green-400">45</p>
        </div>
        <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold text-sage-900 dark:text-sage-100 mb-2">Failed</h3>
            <p class="text-3xl font-bold text-red-600 dark:text-red-400">2</p>
        </div>
    </div>

    <!-- Notification Management Tools -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <!-- Send Broadcast Notification -->
        <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold text-sage-900 dark:text-sage-100 mb-4">Broadcast Notification</h3>
            <p class="text-sage-600 dark:text-sage-300 mb-4">Send a notification to all users</p>
            <form class="space-y-3">
                <input type="text" placeholder="Notification title" 
                       class="w-full px-3 py-2 border border-sage-300 dark:border-sage-600 rounded-md bg-white dark:bg-sage-700 text-sage-900 dark:text-sage-100">
                <textarea placeholder="Message content" rows="3"
                          class="w-full px-3 py-2 border border-sage-300 dark:border-sage-600 rounded-md bg-white dark:bg-sage-700 text-sage-900 dark:text-sage-100"></textarea>
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded transition-colors w-full">
                    Send Broadcast
                </button>
            </form>
        </div>

        <!-- Notification Templates -->
        <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold text-sage-900 dark:text-sage-100 mb-4">Notification Templates</h3>
            <p class="text-sage-600 dark:text-sage-300 mb-4">Manage reusable notification templates</p>
            <div class="space-y-2">
                <button class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded transition-colors w-full text-sm">
                    Watering Reminder
                </button>
                <button class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded transition-colors w-full text-sm">
                    Harvest Alert
                </button>
                <button class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded transition-colors w-full text-sm">
                    Season Planning
                </button>
            </div>
        </div>

        <!-- Notification Settings -->
        <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold text-sage-900 dark:text-sage-100 mb-4">System Settings</h3>
            <p class="text-sage-600 dark:text-sage-300 mb-4">Configure notification system settings</p>
            <div class="space-y-3">
                <label class="flex items-center">
                    <input type="checkbox" checked class="mr-2">
                    <span class="text-sage-700 dark:text-sage-300 text-sm">Enable email notifications</span>
                </label>
                <label class="flex items-center">
                    <input type="checkbox" checked class="mr-2">
                    <span class="text-sage-700 dark:text-sage-300 text-sm">Enable push notifications</span>
                </label>
                <label class="flex items-center">
                    <input type="checkbox" class="mr-2">
                    <span class="text-sage-700 dark:text-sage-300 text-sm">Debug mode</span>
                </label>
            </div>
        </div>
    </div>

    <!-- Recent Notifications -->
    <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md p-6">
        <h3 class="text-xl font-semibold text-sage-900 dark:text-sage-100 mb-4">Recent Notifications</h3>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-sage-200 dark:divide-sage-600">
                <thead class="bg-sage-50 dark:bg-sage-700">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sage-600 dark:text-sage-300 uppercase tracking-wider">User</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sage-600 dark:text-sage-300 uppercase tracking-wider">Type</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sage-600 dark:text-sage-300 uppercase tracking-wider">Message</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sage-600 dark:text-sage-300 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sage-600 dark:text-sage-300 uppercase tracking-wider">Sent</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-sage-800 divide-y divide-sage-200 dark:divide-sage-600">
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-sage-900 dark:text-sage-100">john_doe</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-sage-900 dark:text-sage-100">Watering</td>
                        <td class="px-6 py-4 text-sm text-sage-900 dark:text-sage-100">Time to water your tomatoes</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">Sent</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-sage-500 dark:text-sage-400">2 hours ago</td>
                    </tr>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-sage-900 dark:text-sage-100">jane_smith</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-sage-900 dark:text-sage-100">Harvest</td>
                        <td class="px-6 py-4 text-sm text-sage-900 dark:text-sage-100">Your lettuce is ready for harvest</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800 dark:bg-orange-800 dark:text-orange-100">Pending</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-sage-500 dark:text-sage-400">Scheduled for 1 hour</td>
                    </tr>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-sage-900 dark:text-sage-100">admin</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-sage-900 dark:text-sage-100">System</td>
                        <td class="px-6 py-4 text-sm text-sage-900 dark:text-sage-100">Database backup completed</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">Sent</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-sage-500 dark:text-sage-400">1 day ago</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add form submission handler
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            alert('Broadcast notification functionality will be implemented in a future update.');
        });
    }
});
</script>
{% endblock %}
