{% extends "base.html" %}
{% block title %}Database Management - Admin{% endblock %}
{% block content %}
<div class="max-w-6xl mx-auto">
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-sage-900 dark:text-sage-100">Database Management</h1>
            <p class="text-sage-600 dark:text-sage-400 mt-2">Manage application database and data integrity</p>
        </div>
        <a href="/admin/dashboard" class="bg-sage-600 hover:bg-sage-700 text-white px-4 py-2 rounded-md transition-colors shadow-md">
            Back to Dashboard
        </a>
    </div>

    <!-- Database Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold text-sage-900 dark:text-sage-100 mb-2">Total Users</h3>
            <p class="text-3xl font-bold text-sage-600 dark:text-sage-400">{{ total_users }}</p>
        </div>
        <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold text-sage-900 dark:text-sage-100 mb-2">Total Plants</h3>
            <p class="text-3xl font-bold text-sage-600 dark:text-sage-400">{{ total_plants }}</p>
        </div>
        <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold text-sage-900 dark:text-sage-100 mb-2">Database Health</h3>
            <p class="text-lg font-medium text-green-600 dark:text-green-400">Healthy</p>
        </div>
    </div>

    <!-- Database Operations -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- Backup Database -->
        <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold text-sage-900 dark:text-sage-100 mb-4">Backup Database</h3>
            <p class="text-sage-600 dark:text-sage-300 mb-4">Create a backup of the entire database</p>
            <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded transition-colors w-full">
                Create Backup
            </button>
        </div>

        <!-- Optimize Database -->
        <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold text-sage-900 dark:text-sage-100 mb-4">Optimize Database</h3>
            <p class="text-sage-600 dark:text-sage-300 mb-4">Optimize database performance and clean up</p>
            <button class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded transition-colors w-full">
                Optimize Now
            </button>
        </div>

        <!-- Data Integrity Check -->
        <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold text-sage-900 dark:text-sage-100 mb-4">Data Integrity</h3>
            <p class="text-sage-600 dark:text-sage-300 mb-4">Check for data consistency and integrity issues</p>
            <button class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded transition-colors w-full">
                Run Check
            </button>
        </div>

        <!-- Export Data -->
        <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold text-sage-900 dark:text-sage-100 mb-4">Export Data</h3>
            <p class="text-sage-600 dark:text-sage-300 mb-4">Export application data in various formats</p>
            <div class="space-y-2">
                <button class="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded transition-colors w-full text-sm">
                    Export as JSON
                </button>
                <button class="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded transition-colors w-full text-sm">
                    Export as CSV
                </button>
            </div>
        </div>

        <!-- Import Data -->
        <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold text-sage-900 dark:text-sage-100 mb-4">Import Data</h3>
            <p class="text-sage-600 dark:text-sage-300 mb-4">Import data from external sources</p>
            <input type="file" class="mb-2 w-full text-sm text-sage-600 dark:text-sage-300">
            <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded transition-colors w-full">
                Import Data
            </button>
        </div>

        <!-- Database Maintenance -->
        <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold text-sage-900 dark:text-sage-100 mb-4">Maintenance</h3>
            <p class="text-sage-600 dark:text-sage-300 mb-4">Perform routine database maintenance tasks</p>
            <div class="space-y-2">
                <button class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded transition-colors w-full text-sm">
                    Clean Old Logs
                </button>
                <button class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded transition-colors w-full text-sm">
                    Update Statistics
                </button>
            </div>
        </div>
    </div>

    <!-- Recent Activity Log -->
    <div class="mt-8 bg-white dark:bg-sage-800 rounded-lg shadow-md p-6">
        <h3 class="text-xl font-semibold text-sage-900 dark:text-sage-100 mb-4">Recent Database Activity</h3>
        <div class="text-sage-600 dark:text-sage-300">
            <p class="mb-2">• Database backup completed successfully - 2 hours ago</p>
            <p class="mb-2">• Data integrity check passed - 1 day ago</p>
            <p class="mb-2">• Database optimization completed - 3 days ago</p>
            <p class="text-sage-500 dark:text-sage-400 text-sm mt-4">Activity logs are automatically cleaned after 30 days</p>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add click handlers for database operations
    const buttons = document.querySelectorAll('button');
    buttons.forEach(button => {
        button.addEventListener('click', function() {
            const operation = this.textContent.trim();
            if (operation !== 'Back to Dashboard') {
                alert(`${operation} functionality will be implemented in a future update.`);
            }
        });
    });
});
</script>
{% endblock %}
