{% extends "base.html" %}
{% block title %}Admin Dashboard{% endblock %}
{% block content %}
<div class="max-w-6xl mx-auto">
    <h1 class="text-3xl font-bold mb-6">Admin Dashboard</h1>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- User Management -->
        <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold text-sage-900 dark:text-sage-100 mb-4">User Management</h2>
            <p class="text-sage-600 dark:text-sage-300 mb-4">Manage users, roles, and permissions</p>
            <a href="/admin/users" class="bg-sage-600 hover:bg-sage-700 text-white font-medium px-4 py-2 rounded transition-colors shadow-md">
                Manage Users
            </a>
        </div>

        <!-- System Statistics -->
        <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold text-sage-900 dark:text-sage-100 mb-4">System Statistics</h2>
            <div class="space-y-2">
                <p class="text-sage-600 dark:text-sage-300">Total Users: <span class="font-semibold text-sage-900 dark:text-sage-100">{{ total_users | default(value="N/A") }}</span></p>
                <p class="text-sage-600 dark:text-sage-300">Total Plants: <span class="font-semibold text-sage-900 dark:text-sage-100">{{ total_plants | default(value="N/A") }}</span></p>
                <p class="text-sage-600 dark:text-sage-300">Total Properties: <span class="font-semibold text-sage-900 dark:text-sage-100">{{ total_properties | default(value="N/A") }}</span></p>
            </div>
        </div>

        <!-- System Settings -->
        <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold text-sage-900 dark:text-sage-100 mb-4">System Settings</h2>
            <p class="text-sage-600 dark:text-sage-300 mb-4">Configure system-wide settings</p>
            <a href="/admin/settings" class="bg-sage-600 hover:bg-sage-700 text-white font-medium px-4 py-2 rounded transition-colors shadow-md">
                Settings
            </a>
        </div>

        <!-- Database Management -->
        <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold text-sage-900 dark:text-sage-100 mb-4">Database Management</h2>
            <p class="text-sage-600 dark:text-sage-300 mb-4">Backup, restore, and maintain database</p>
            <a href="/admin/database" class="bg-sage-700 hover:bg-sage-800 text-white font-medium px-4 py-2 rounded transition-colors shadow-md">
                Database Tools
            </a>
        </div>

        <!-- HerbaDB Management -->
        <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold text-sage-900 dark:text-sage-100 mb-4">HerbaDB Management</h2>
            <p class="text-sage-600 dark:text-sage-300 mb-4">Manage global plant database and botanical information</p>
            <a href="/admin/herba-db" class="bg-sage-700 hover:bg-sage-800 text-white font-medium px-4 py-2 rounded transition-colors shadow-md">
                Manage HerbaDB
            </a>
        </div>

        <!-- Notifications -->
        <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold text-sage-900 dark:text-sage-100 mb-4">Notification System</h2>
            <p class="text-sage-600 dark:text-sage-300 mb-4">Monitor and manage notifications</p>
            <a href="/admin/notifications" class="bg-sage-700 hover:bg-sage-800 text-white px-4 py-2 rounded transition-colors">
                Notifications
            </a>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="mt-8 bg-white dark:bg-sage-800 rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold text-sage-900 dark:text-sage-100 mb-4">Recent Activity</h2>
        <div class="text-sage-600 dark:text-sage-300">
            <p>Recent activity logs will be displayed here...</p>
        </div>
    </div>
</div>
{% endblock %}
