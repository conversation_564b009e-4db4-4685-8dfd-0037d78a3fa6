{% extends "base.html" %}
{% block title %}Seed Wishlist{% endblock %}
{% block content %}
<div class="max-w-6xl mx-auto">
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-sage-900 dark:text-sage-100">Seed Wishlist</h1>
            <p class="text-sage-600 dark:text-sage-400 mt-2">Seeds you want to acquire</p>
        </div>
        <div class="flex space-x-2">
            <a href="/wishlist/plants" class="bg-sage-600 hover:bg-sage-700 text-white px-4 py-2 rounded transition-colors shadow-md">
                Plant Wishlist
            </a>
            <a href="/seeds/new" class="bg-sage-700 hover:bg-sage-800 text-white px-4 py-2 rounded transition-colors shadow-md">
                Add New Seed
            </a>
        </div>
    </div>

    <!-- Seed Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {% for seed in seeds %}
        <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
            <div class="p-4">
                <div class="flex items-center mb-3">
                    <div class="w-12 h-12 bg-amber-100 dark:bg-amber-900 rounded-full flex items-center justify-center mr-3">
                        <svg class="w-6 h-6 text-amber-600 dark:text-amber-300" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                        </svg>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-lg font-semibold text-sage-900 dark:text-sage-100">{{ seed.name }}</h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400">{{ seed.variety | default(value="Standard variety") }}</p>
                    </div>
                </div>

                <div class="space-y-2 mb-4">
                    {% if seed.plant_name %}
                    <p class="text-sm text-gray-600 dark:text-gray-300">
                        <span class="font-medium">Plant:</span> {{ seed.plant_name }}
                    </p>
                    {% endif %}

                    {% if seed.supplier %}
                    <p class="text-sm text-gray-600 dark:text-gray-300">
                        <span class="font-medium">Supplier:</span> {{ seed.supplier }}
                    </p>
                    {% endif %}

                    {% if seed.germination_rate %}
                    <p class="text-sm text-gray-600 dark:text-gray-300">
                        <span class="font-medium">Germination:</span> {{ seed.germination_rate }}%
                    </p>
                    {% endif %}

                    {% if seed.notes %}
                    <p class="text-sm text-gray-600 dark:text-gray-300">
                        {{ seed.notes | truncate(length=100) }}
                    </p>
                    {% endif %}
                </div>

                <div class="flex space-x-2">
                    {% if seed.id in wishlist_ids %}
                    <form method="post" action="/wishlist/remove" class="flex-1">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                        <input type="hidden" name="item_type" value="seed">
                        <input type="hidden" name="item_id" value="{{ seed.id }}">
                        <button type="submit" class="w-full bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded text-sm transition-colors">
                            Remove from Wishlist
                        </button>
                    </form>
                    {% else %}
                    <form method="post" action="/wishlist/add" class="flex-1">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                        <input type="hidden" name="item_type" value="seed">
                        <input type="hidden" name="item_id" value="{{ seed.id }}">
                        <button type="submit" class="w-full bg-amber-600 hover:bg-amber-700 text-white px-3 py-2 rounded text-sm transition-colors">
                            Add to Wishlist
                        </button>
                    </form>
                    {% endif %}
                    <a href="/seeds/{{ seed.id }}" class="bg-gray-600 hover:bg-gray-700 text-white px-3 py-2 rounded text-sm transition-colors">
                        View
                    </a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    {% if seeds|length == 0 %}
    <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"></path>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No seeds available</h3>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Get started by adding some seeds to the database.</p>
        <div class="mt-6">
            <a href="/seeds/new" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-amber-600 hover:bg-amber-700">
                Add Seed
            </a>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
