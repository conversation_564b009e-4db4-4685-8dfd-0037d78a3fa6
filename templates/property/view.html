{% extends "base.html" %}
{% block title %}Property View{% endblock %}
{% block content %}
<div class="max-w-6xl mx-auto">
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-sage-900 dark:text-sage-100">{{ property.name }}</h1>
            <p class="text-sage-600 dark:text-sage-400 mt-2">Property visualization and details</p>
        </div>
        <div class="flex space-x-3">
            <a href="/property/{{ property.id }}/edit" class="bg-sage-600 hover:bg-sage-700 text-white px-4 py-2 rounded-md transition-colors shadow-md">
                Edit Property
            </a>
            <a href="/property" class="bg-sage-100 hover:bg-sage-200 dark:bg-sage-700 dark:hover:bg-sage-600 text-sage-700 dark:text-sage-200 px-4 py-2 rounded-md transition-colors">
                Back to Properties
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md p-6">
            <h3 class="text-xl font-semibold mb-4 text-sage-900 dark:text-sage-100">Property Details</h3>
            <div class="space-y-3">
                <div class="flex justify-between items-center py-2 border-b border-sage-200 dark:border-sage-600">
                    <span class="text-sage-600 dark:text-sage-400">Inside Area:</span>
                    <span class="font-medium text-sage-900 dark:text-sage-100">{{ property.inside_area }} m²</span>
                </div>
                <div class="flex justify-between items-center py-2 border-b border-sage-200 dark:border-sage-600">
                    <span class="text-sage-600 dark:text-sage-400">Outside Area:</span>
                    <span class="font-medium text-sage-900 dark:text-sage-100">{{ property.outside_area }} m²</span>
                </div>
                <div class="flex justify-between items-center py-2">
                    <span class="text-sage-600 dark:text-sage-400">Floors:</span>
                    <span class="font-medium text-sage-900 dark:text-sage-100">{{ property.floors }}</span>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md p-6">
            <h3 class="text-xl font-semibold mb-4 text-sage-900 dark:text-sage-100">Growing Areas</h3>
            <div class="space-y-3">
                <div class="flex justify-between items-center py-2 border-b border-sage-200 dark:border-sage-600">
                    <span class="text-sage-600 dark:text-sage-400">Total Growing Area:</span>
                    <span class="font-medium text-sage-900 dark:text-sage-100">{{ total_growing_area|default(value=0) }} m²</span>
                </div>
                <div class="flex justify-between items-center py-2 border-b border-sage-200 dark:border-sage-600">
                    <span class="text-sage-600 dark:text-sage-400">Outside Growing Area:</span>
                    <span class="font-medium text-sage-900 dark:text-sage-100">{{ outside_growing_area|default(value=0)|abs }} m²</span>
                </div>
                <div class="flex justify-between items-center py-2">
                    <span class="text-sage-600 dark:text-sage-400">Inside Growing Area:</span>
                    <span class="font-medium text-sage-900 dark:text-sage-100">{{ inside_growing_area|default(value=0) }} m²</span>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md p-6 mb-6">
        <h3 class="text-xl font-semibold mb-4 text-sage-900 dark:text-sage-100">Floor Selector</h3>
        <div class="flex flex-wrap gap-3">
            <a href="?floor=-1" class="px-4 py-2 bg-sage-600 hover:bg-sage-700 text-white rounded-md transition-colors shadow-sm {% if current_floor == -1 %}bg-sage-800 cursor-not-allowed{% endif %}">
                Outside
            </a>
            {% for i in range(end=property.floors) %}
            <a href="?floor={{ i }}" class="px-4 py-2 bg-sage-600 hover:bg-sage-700 text-white rounded-md transition-colors shadow-sm {% if current_floor == i %}bg-sage-800 cursor-not-allowed{% endif %}">
                Floor {{ i }}
            </a>
            {% endfor %}
        </div>
    </div>

    <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md p-6 mb-6">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-xl font-semibold text-sage-900 dark:text-sage-100">{{ current_floor_name }} Layout</h3>
            <div class="text-sm text-sage-600 dark:text-sage-400">
                Floor: <span class="font-medium text-sage-900 dark:text-sage-100">{{ current_floor_name }}</span>
            </div>
        </div>
        <div class="relative w-full h-96 bg-sage-50 dark:bg-sage-900 rounded-lg border border-sage-200 dark:border-sage-600 overflow-hidden">
            <canvas id="viewCanvas" class="w-full h-full"></canvas>
            <div id="canvas-error" class="hidden absolute inset-0 flex items-center justify-center">
                <div class="text-center p-6">
                    <div class="text-sage-400 dark:text-sage-500 mb-2">
                        <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <p class="text-sage-600 dark:text-sage-400 font-medium">Error loading property layout</p>
                    <p class="text-sage-500 dark:text-sage-500 text-sm mt-1">Please check the console for details</p>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md p-6">
        <h3 class="text-xl font-semibold mb-4 text-sage-900 dark:text-sage-100">Growing Areas on {{ current_floor_name }}</h3>
        {% if growing_areas %}
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {% for area in growing_areas %}
            <div class="bg-sage-50 dark:bg-sage-700 p-4 rounded-lg border border-sage-200 dark:border-sage-600 hover:shadow-md transition-shadow">
                <h4 class="font-medium text-sage-900 dark:text-sage-100 mb-2">{{ area.shape_type|title }}</h4>
                <div class="space-y-1">
                    <p class="text-sage-600 dark:text-sage-400 text-sm">
                        <span class="font-medium">Area:</span> {{ area.area|default(value="N/A") }} m²
                    </p>
                    {% if area.floor is defined %}
                    <p class="text-sage-600 dark:text-sage-400 text-sm">
                        <span class="font-medium">Floor:</span> {{ area.floor }}
                    </p>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="text-center py-8">
            <div class="text-sage-400 dark:text-sage-500 mb-3">
                <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>
            </div>
            <p class="text-sage-600 dark:text-sage-400 font-medium">No growing areas defined for this floor</p>
            <p class="text-sage-500 dark:text-sage-500 text-sm mt-1">Use the Property Wizard to add growing areas</p>
            <a href="/property/wizard" class="inline-block mt-4 bg-sage-600 hover:bg-sage-700 text-white px-4 py-2 rounded-md transition-colors text-sm">
                Add Growing Areas
            </a>
        </div>
        {% endif %}
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const canvas = document.getElementById('viewCanvas');
        const ctx = canvas.getContext('2d');

        // Set canvas size to match container size
        function resizeCanvas() {
            const container = canvas.parentElement;
            canvas.width = container.clientWidth;
            canvas.height = container.clientHeight;
            drawLayout();
        }

        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        function drawLayout() {
            // Clear canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            try {
                // Draw property boundaries
                const propertyShapes = {{ property_shapes|safe }};
                console.log('Property shapes:', propertyShapes);
                if (Array.isArray(propertyShapes) && propertyShapes.length > 0) {
                    propertyShapes.forEach(shape => {
                        drawShape(shape, 'rgba(128, 128, 128, 0.2)', 'rgba(128, 128, 128, 0.8)');
                    });
                } else {
                    console.log('No property shapes found');
                }

                // Draw growing areas
                const growingAreas = {{ growing_areas_json|safe }};
                console.log('Growing areas:', growingAreas);
                if (Array.isArray(growingAreas) && growingAreas.length > 0) {
                    growingAreas.forEach(shape => {
                        drawShape(shape, 'rgba(76, 175, 80, 0.3)', 'rgba(76, 175, 80, 0.8)');
                    });
                } else {
                    console.log('No growing areas found');
                }

                // If no shapes, show a helpful message
                if ((!propertyShapes || propertyShapes.length === 0) &&
                    (!growingAreas || growingAreas.length === 0)) {
                    ctx.fillStyle = 'rgba(128, 128, 128, 0.7)';
                    ctx.font = '16px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('No property layout available for this floor', canvas.width / 2, canvas.height / 2 - 10);
                    ctx.fillText('Use the Property Wizard to create a layout', canvas.width / 2, canvas.height / 2 + 10);
                }
            } catch (error) {
                console.error('Error drawing layout:', error);
                // Show error state
                document.getElementById('canvas-error').classList.remove('hidden');
            }
        }

        function drawShape(shape, fillColor, strokeColor) {
            try {
                let points = [];

                // Handle different shape data formats
                if (shape.points) {
                    points = shape.points;
                } else if (shape.shape_data) {
                    if (typeof shape.shape_data === 'string') {
                        points = JSON.parse(shape.shape_data);
                    } else {
                        points = shape.shape_data;
                    }
                }

                if (!Array.isArray(points) || points.length === 0) {
                    console.warn('Invalid points data for shape:', shape);
                    return;
                }

                ctx.beginPath();
                ctx.fillStyle = fillColor;
                ctx.strokeStyle = strokeColor;
                ctx.lineWidth = 2;

                // Draw as polygon/freehand by default
                ctx.moveTo(points[0].x, points[0].y);
                for (let i = 1; i < points.length; i++) {
                    ctx.lineTo(points[i].x, points[i].y);
                }
                ctx.closePath();

                ctx.fill();
                ctx.stroke();
            } catch (error) {
                console.error('Error drawing shape:', error, shape);
            }
        }
    });
</script>
{% endblock %}
